<template>
  <div class="top">
    <div class="top-left" v-if="details">
      <div class="block">
        <img src="@/assets/images/benefit1.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">项目经理</div>
          <div style="color: #7d23ff">{{ details.project_manager }}</div>
          <div style="color: #c4c4c4">高级工程师</div>
        </div>
      </div>
      <div class="block">
        <img src="@/assets/images/benefit3.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">合同金额</div>
          <div style="color: #c4c4c4">{{ details.f_contract_amount }}</div>
          <div style="color: #c4c4c4">{{ getAmountChinese(details.f_contract_amount) }}</div>
        </div>
      </div>
      <div class="block">
        <img src="@/assets/images/benefit2.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">执行经理</div>
          <div style="color: #fb9500">{{ details.execution_manager }}</div>
          <div style="color: #c4c4c4">高级工程师</div>
        </div>
      </div>
      <div class="block">
        <img src="@/assets/images/benefit3.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">效益金额</div>
          <div style="color: #c4c4c4">{{ details.f_allocation_amount }}</div>
          <div style="color: #c4c4c4">{{ getAmountChinese(details.f_allocation_amount) }}</div>
        </div>
      </div>
    </div>
    <div class="top-right">
      <div class="titleE">产值分配情况</div>
      <div class="titleC">output</div>
      <div class="top-right-bottom">
        <a-popover
          :title="item.f_real_name"
          placement="right"
          v-for="item in source"
          class="top-right-bottom-item"
        >
          <template #content>
            <p>产值分配：{{ item.f_allocation_amount }}</p>
          </template>
          <div>
            <div class="top-right-bottom-item-di">
              <div
                class="top-right-bottom-item-inner"
                :style="{ height: item.f_allocation_ratio + '%' }"
              ></div>
            </div>
            <div style="color: #828282; margin-top: 5px; width: 150%">{{ item.f_real_name }}</div>
          </div>
        </a-popover>
      </div>
    </div>
  </div>
  <!-- <div> -->
  <div class="bottom">
    <div class="bottom-one">
      <div class="titleE">复核平均分</div>
      <div class="titleC">output</div>
      <lineChart />
    </div>
    <div class="bottom-two">
      <div class="titleE">任务量占比</div>
      <div class="titleC">output</div>
      <pieChart />
    </div>
    <div class="bottom-three">
      <div class="titleE">工时量占比</div>
      <div class="titleC">output</div>
       <pieChart />
    </div>
  </div>
  <!-- </div> -->
  <div class="table-outer">
    <a-table :columns="columns" :data-source="source" bordered class="table">
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'name'">
          <a>{{ text }}</a>
        </template>
      </template>
      <template #title>
        <div style="display: flex; align-items: center; justify-content: space-between">
          <div style="font-weight: 900; font-size: 18px">项目任务执行情况</div>
          <a-button type="primary" @click="showModal">效益分配</a-button>
        </div>
      </template>
    </a-table>
  </div>

  <a-modal v-model:open="open" width="800px" title="项目成员产值分配" @ok="handleOk">
    <div class="source">
      <div style="display: flex; margin-bottom: 20px" v-if="details">
        <a-input-number
          style="width: 40% !important; margin-right: 20px; background-color: #eaeff4"
          size="large"
          addon-before="合同金额"
          addon-after="元"
          :disabled="true"
          v-model:value="modalData.f_contract_amount"
        >
        </a-input-number>
        <a-input-number
          style="background-color: #eaeff4"
          size="large"
          addon-before="效益金额"
          addon-after="元"
          @change="changeAmount"
          v-model:value="modalData.f_allocation_amount"
        >
        </a-input-number>
      </div>
      <div v-for="item in modalData.source" class="source-item">
        <!-- <div style="border: 1px solid #ddd; display: flex; width: 40%; margin-right: 20px">
          <div style="padding: 0 15px; line-height: 40px; width: 30%; background-color: #eaeff4">
            {{ item.f_real_name }}
          </div>
          <div style="padding: 0 15px; line-height: 40px; width: 70%; background-color: #eaeff4">
            {{ item.f_allocation_amount }}
          </div>
        </div> -->
        <a-input-number
          style="width: 40% !important; margin-right: 20px; background-color: #eaeff4"
          size="large"
          :addon-before="item.f_real_name"
          addon-after="元"
          :disabled="true"
          v-model:value="item.f_allocation_amount"
        >
        </a-input-number>
        <a-input-number
          style="background-color: #eaeff4"
          size="large"
          v-model:value="item.f_allocation_ratio"
          addon-after="%"
          :min="1"
          :max="100"
          @change="(e) => changeProgress(e, item)"
        >
        </a-input-number>
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue";
import { Modal as AModal } from "ant-design-vue";
import { onlineUtils, getAmountChinese } from "@/utils/jnpf";
import { updateModel, getModelInfo, getModelList } from "@/api/onlineDev/visualDev";
import { getUserInfo } from "@/api/permission/user";
import pieChart from "./components/pieChart.vue";
import lineChart from "./components/lineChart.vue";
import type { TableColumnType } from "ant-design-vue";
const details = ref<any>(null);
const open = ref<boolean>(false);
const source = ref<any>();
const modalData = ref<any>(null);

const columns = [
  {
    title: "任务人",
    dataIndex: "f_real_name",
  },
  {
    title: "分配占比%",
    dataIndex: "f_allocation_ratio",
  },
  {
    title: "分配金额",
    dataIndex: "f_allocation_amount",
  },
  {
    title: "任务量",
    dataIndex: "name",
  },
    {
    title: "累计工时",
    dataIndex: "name",
  },
    {
    title: "复核评分",
    dataIndex: "name",
  }
];

const showModal = () => {
  modalData.value = {
    f_contract_amount: details.value.f_contract_amount,
    f_allocation_amount: details.value.f_allocation_amount,
    source: JSON.parse(JSON.stringify(source.value)),
  };
  open.value = true;
};
const handleOk = (e: MouseEvent) => {
  details.value.f_allocation_amount = modalData.value.f_allocation_amount;
  details.value.tableField3ce712.forEach((element) => {
    let current = modalData.value.source.find((item) => item.f_user_id == element.f_user_id);
    element.f_allocation_amount = current.f_allocation_amount;
    element.f_allocation_ratio = current.f_allocation_ratio;
  });
  updateModel("708293041642802053", {
    id: details.value.id,
    data: JSON.stringify(details.value),
  }).then((res) => {
    if (res.code == 200) {
      init();
    }
  });
  open.value = false;
};
const changeProgress = (e, item) => {
  item.f_allocation_amount = Math.round(modalData.value.f_allocation_amount * (e / 100));
};
const changeAmount = (e) => {
  modalData.value.source.forEach((element) => {
    element.f_allocation_amount = Math.round(e * (element.f_allocation_ratio / 100));
  });
};

async function init() {
  var dataParam = {};
  dataParam.tenantId = "";
  var paramList = [];
  var codingParam = {
    id: "4de991",
    field: "project_code",
    defaultValue: localStorage.getItem("project_code"),
    fieldName: "",
    dataType: "varchar",
    required: 0,
  };
  paramList.push(codingParam);
  dataParam.paramList = paramList;
  let resData = await onlineUtils.request(
    "/api/system/DataInterface/724620672612963077/Actions/Preview?tenantId=0",
    "post",
    dataParam
  );
  source.value = resData?.data;
  getModelInfo("708293041642802053", localStorage.getItem("project_id")).then((res) => {
    details.value = JSON.parse(res.data.data);
    getUserInfo(details.value.f_project_manager_id).then((res) => {
      details.value.project_manager = res.data.realName;
    });
    getUserInfo(details.value.f_execution_manager_id).then((res) => {
      details.value.execution_manager = res.data.realName;
    });
  });
}

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.titleE {
  font-weight: 900;
  font-size: 17px;
}
.titleC {
  color: #aeaeae;
  font-size: 13px;
}
.top {
  padding: 30px;
  box-sizing: border-box;
  display: flex;
  // min-width: 1500px;
  justify-content: space-between;
  &-left {
    display: flex;
    gap: 7%;
    width: 40%;
    flex-wrap: wrap;
    align-self: safe;
    .block {
      width: 46.5%;
      border-radius: 5px;
      background-color: #fff;
      display: flex;
      align-items: center;
      padding: 15px 2%;
      box-sizing: border-box;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
      line-height: 23px;
      font-size: 17px;
      img {
        padding-right: 10px;
        width: 35%;
      }
    }
  }
  &-right {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 58%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
    &-bottom {
      width: 100%;
      margin-top: 40px;
      display: flex;
      overflow-x: auto;
      gap: 5%;
      &-item {
        height: 100%;
        width: 4.6%;
        flex-shrink: 0;
        cursor: pointer;
        &-di {
          width: 100%;
          height: 160px;
          background-color: #e9ecf1;
          border-radius: 7px;
          position: relative;
        }
        &-inner {
          width: 100%;
          position: absolute;
          background-color: #1a9dff;
          border-radius: 7px;
          bottom: 0;
          left: 0;
          right: 0;
        }
      }
    }
  }
}
.bottom {
  width: 100%;
  padding: 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  &-one {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 40%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
  }
  &-two {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 28%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
  }
  &-three {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 28%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
  }
}
.table-outer {
  padding: 30px;
  box-sizing: border-box;
}
.table {
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
}
.ant-input-number,
.ant-input-number-group-wrapper {
  width: 60% !important;
}
.ant-input-number .ant-input-number-input {
  height: 100% !important;
}
.source {
  width: 100%;
  padding: 30px;
  &-item {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;
    border-radius: 3px;

    border-right: 0;
  }
}
</style>
