import { withInstall } from '@/utils';
import UploadBtn from './src/UploadBtn.vue';
import UploadFile from './src/UploadFile.vue';
import UploadFruitFile from './src/HzUploadFruitFile.vue';
import UploadImg from './src/UploadImg.vue';
import UploadImgSingle from './src/UploadImgSingle.vue';

export const JnpfUploadBtn = withInstall(UploadBtn);
export const JnpfUploadFile = withInstall(UploadFile);
export const HzUploadFruitFile = withInstall(UploadFruitFile);
export const JnpfUploadImg = withInstall(UploadImg);
export const JnpfUploadImgSingle = withInstall(UploadImgSingle);
