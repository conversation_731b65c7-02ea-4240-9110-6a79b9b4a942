<template>
  <BasicModal v-bind="$attrs" @register="registerModal" isNoTitle :title="title" :footer="null" width="1000px" :minHeight="600" class="flow-parser-modal">
    <FlowParser :isNoTitle="true" @register="registerFlowParser" @reload="handleReload" @close="handleClose" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { nextTick } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import FlowParser from '@/views/workFlow/components/FlowParser.vue';

  const emit = defineEmits(['register', 'reload', 'close']);
  const props = defineProps({
    title: { type: String, default: '流程处理' },
  });
  const [registerModal, { closeModal }] = useModalInner(init);
  const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();

  function init(data) {
    nextTick(() => {
      openFlowParser(true, data);
    });
  }

  function handleReload() {
    emit('reload');
    closeModal();
  }

  function handleClose() {
    emit('close');
    closeModal();
  }
</script>
<style lang="less" >
.flow-parser-modal {
  .scrollbar.scroll-container {
    padding: 0!important;
  }
  .flow-form-container {
    padding: 20px!important;
  }
}
</style>