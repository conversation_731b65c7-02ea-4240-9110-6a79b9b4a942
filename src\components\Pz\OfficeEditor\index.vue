<template>
  <DocumentEditor
    v-if="state.config"
    :id="state.file?.fileId"
    documentServerUrl="http://************:8060"
    :config="state.config" />
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';

  defineExpose({ init, close });

  // 将原来的const ref变量封装到state中
  const state = reactive({
    config: null,
    file: null,
  });

  async function init(file) {
    state.file = file;
    state.config = {
      width: '100%',
      height: '100%',
      type: 'desktop', // 或 'mobile'
      document: {
        fileType: file.fileExtension, // docx/xlsx...
        key: file.fileId.replace(',', '-') + 'delme', // 唯一标识
        title: file.name,
        url: 'http://************:3100/dev' + file.url,
        permissions: {
          edit: true,
          download: true,
          print: true,
        },
      },
      editorConfig: {
        callbackUrl: 'http://************:30000/api/onlyoffice/callback',
        mode: 'edit', // coedit/view/comment
        lang: 'zh',
        user: { id: 'u1', name: '张三' },
      }, // 如果启用 JWT，这里要加 token 字段；DS 也要加 token
    };
  }

  function close() {
    state.config = null;
    state.file = null;
  }
</script>
