<template>
  <div ref="chartRef" style="width: 100%; height: 300px"></div>
</template>
<script lang="ts" setup>
import { useEChart } from "@/components/VisualPortal/Design/hooks/useEChart";
import { onMounted, ref, Ref, watch } from "vue";

// const props = defineProps(["grouped"]);

let activeData = {
  jnpfKey: "pieChart",
  dataType: "static",
  option: {
    styleType: 2,
    defaultValue: [
      {
        name: "高点点",
        type: "手机品牌",
        value: 1000879,
      },
      {
        name: "高点",
        type: "手机品牌",
        value: 3400879,
      },
      {
        name: "点",
        type: "手机品牌",
        value: 2300879,
      },
      {
        name: "张三",
        type: "手机品牌",
        value: 5400879,
      },
      {
        name: "李四",
        type: "手机品牌",
        value: 3400879,
      },
      {
        name: "网二",
        type: "手机品牌",
        value: 3400879,
      },
      {
        name: "角斗士",
        type: "手机品牌",
        value: 3400879,
      },
    ],
    roseType: false,
    roseType1: false,
    titleText: "",
    titleTextStyleColor: "#303133",
    titleTextStyleFontSize: 18,
    titleTextStyleFontWeight: false,
    titleLeft: "center",
    titleBgColor: "",
    titleSubtext: "",
    titleSubtextStyleColor: "#303133",
    titleSubtextStyleFontSize: null,
    titleSubtextStyleFontWeight: false,
    seriesLabelShow: false,
    seriesLabelPosition: "outside",
    seriesLabelShowInfo: ["count", "percent"],
    seriesCenterLeft: 50,
    seriesCenterTop: 30,
    seriesLabelFontSize: 14,
    seriesLabelFontWeight: false,
    seriesLabelColor: "#303133",
    seriesLabelBgColor: "",
    seriesLineStyleWidth: 20,
    seriesSymbolRotate: 4,
    tooltipShow: true,
    tooltipTextStyleFontSize: 14,
    tooltipTextStyleFontWeight: false,
    tooltipTextStyleColor: "#303133",
    tooltipBgColor: "#fff",
    gridLeft:50,
    gridTop: 0,
    gridRight: 10,
    gridBottom: 0,
    legendShow: true,
    legendTextStyleFontSize: 14,
    legendOrient: "horizontal",
    legendLeft: 0,
    legendTop: null,
    legendBottom: 10,
    AxisTextStyleColor: "",
    AxisLineStyleColor: "",
    colorList: [],
    target: "_self",
    urlAddress: "",
    sortable: true,
  },
  refresh: {
    autoRefresh: false,
    autoRefreshTime: 5,
  },
};

const chartRef = ref<HTMLDivElement | null>(null);
const { CardHeader, init } = useEChart(activeData, chartRef as Ref<HTMLDivElement>);
onMounted(() => init());
// watch(
//   async () => props.grouped,
//   (newVal, oldVal) => {
//     if (newVal) {
//       let current = JSON.parse(JSON.stringify(props.grouped));
//       activeData.option.defaultValue = current.map((item) => {
//         return {
//           name: item.f_custom_task_name,
//           type: "任务进度",
//           value: item.f_progress,
//         };
//       });
//       init();
//     }
//   }
// );
</script>
